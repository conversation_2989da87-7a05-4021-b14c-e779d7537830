##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.1"
options:
    F103_Moduel:
        files: {}
        virtualPathFiles:
            <virtual_root>/Application/User/Core/freertos.c: ""
            <virtual_root>/Application/User/Core/stm32f1xx_hal_timebase_tim.c: ""
            <virtual_root>/Middlewares/FreeRTOS/croutine.c: ""
            <virtual_root>/Middlewares/FreeRTOS/event_groups.c: ""
            <virtual_root>/Middlewares/FreeRTOS/list.c: ""
            <virtual_root>/Middlewares/FreeRTOS/queue.c: ""
            <virtual_root>/Middlewares/FreeRTOS/stream_buffer.c: ""
            <virtual_root>/Middlewares/FreeRTOS/tasks.c: ""
            <virtual_root>/Middlewares/FreeRTOS/timers.c: ""
            <virtual_root>/Middlewares/FreeRTOS/cmsis_os2.c: ""
            <virtual_root>/Middlewares/FreeRTOS/heap_4.c: ""
            <virtual_root>/Middlewares/FreeRTOS/port.c: ""
