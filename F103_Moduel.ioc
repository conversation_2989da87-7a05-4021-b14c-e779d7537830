#MicroXplorer Configuration settings - do not modify
FREERTOS.IPParameters=Tasks01
FREERTOS.Tasks01=defaultTask,24,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32F1
Mcu.IP0=FREERTOS
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=USART1
Mcu.IP5=USART3
Mcu.IPNb=6
Mcu.Name=STM32F103Z(C-D-E)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE6
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PE14
Mcu.Pin11=PE15
Mcu.Pin12=PD8
Mcu.Pin13=PD9
Mcu.Pin14=PC6
Mcu.Pin15=PA9
Mcu.Pin16=PA10
Mcu.Pin17=PA13
Mcu.Pin18=PA14
Mcu.Pin19=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=VP_SYS_VS_tim8
Mcu.Pin3=PF7
Mcu.Pin4=PF8
Mcu.Pin5=PF9
Mcu.Pin6=PF10
Mcu.Pin7=OSC_IN
Mcu.Pin8=OSC_OUT
Mcu.Pin9=PF11
Mcu.PinsNb=21
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103ZETx
MxCube.Version=6.3.0
MxDb.Version=DB.6.0.30
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true
NVIC.TIM8_UP_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true
NVIC.TimeBase=TIM8_UP_IRQn
NVIC.TimeBaseIP=TIM8
NVIC.USART3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
OSC_IN.Mode=HSE-External-Oscillator
OSC_IN.Signal=RCC_OSC_IN
OSC_OUT.Mode=HSE-External-Oscillator
OSC_OUT.Signal=RCC_OSC_OUT
PA10.GPIOParameters=GPIO_PuPd,GPIO_Label
PA10.GPIO_Label=USART1_RX
PA10.GPIO_PuPd=GPIO_PULLUP
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=USART1_TX
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=INB
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC6.Locked=true
PC6.PinState=GPIO_PIN_SET
PC6.Signal=GPIO_Output
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Locked=true
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE14.GPIOParameters=GPIO_PuPd,GPIO_Label
PE14.GPIO_Label=K1
PE14.GPIO_PuPd=GPIO_PULLUP
PE14.Locked=true
PE14.Signal=GPIO_Input
PE15.GPIOParameters=GPIO_PuPd,GPIO_Label
PE15.GPIO_Label=K2
PE15.GPIO_PuPd=GPIO_PULLUP
PE15.Locked=true
PE15.Signal=GPIO_Input
PE6.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PE6.GPIO_Label=INA
PE6.GPIO_PuPd=GPIO_PULLUP
PE6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PE6.Locked=true
PE6.PinState=GPIO_PIN_SET
PE6.Signal=GPIO_Output
PF10.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PF10.GPIO_Label=SCL
PF10.GPIO_PuPd=GPIO_PULLUP
PF10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PF10.Locked=true
PF10.PinState=GPIO_PIN_SET
PF10.Signal=GPIO_Output
PF11.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PF11.GPIO_Label=SDA
PF11.GPIO_PuPd=GPIO_PULLUP
PF11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PF11.Locked=true
PF11.PinState=GPIO_PIN_SET
PF11.Signal=GPIO_Output
PF7.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PF7.GPIO_Label=WHITE
PF7.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PF7.GPIO_PuPd=GPIO_PULLUP
PF7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PF7.Locked=true
PF7.PinState=GPIO_PIN_SET
PF7.Signal=GPIO_Output
PF8.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PF8.GPIO_Label=BLUE
PF8.GPIO_PuPd=GPIO_PULLUP
PF8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PF8.Locked=true
PF8.PinState=GPIO_PIN_SET
PF8.Signal=GPIO_Output
PF9.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PF9.GPIO_Label=GREEN
PF9.GPIO_PuPd=GPIO_PULLUP
PF9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PF9.Locked=true
PF9.PinState=GPIO_PIN_SET
PF9.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.4
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=F103_Moduel.ioc
ProjectManager.ProjectName=F103_Moduel
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_USART1_UART_Init-USART1-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FSMCFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.I2S2Freq_Value=72000000
RCC.I2S3Freq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FSMCFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SDIOFreq_Value=72000000
RCC.SDIOHCLKDiv2FreqValue=36000000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim8.Mode=TIM8
VP_SYS_VS_tim8.Signal=SYS_VS_tim8
board=custom
