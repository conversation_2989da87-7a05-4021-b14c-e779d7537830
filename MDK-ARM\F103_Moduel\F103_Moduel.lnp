--cpu Cortex-M3
"f103_moduel\startup_stm32f103xe.o"
"f103_moduel\main.o"
"f103_moduel\gpio.o"
"f103_moduel\freertos.o"
"f103_moduel\usart.o"
"f103_moduel\stm32f1xx_it.o"
"f103_moduel\stm32f1xx_hal_msp.o"
"f103_moduel\stm32f1xx_hal_timebase_tim.o"
"f103_moduel\stm32f1xx_hal_gpio_ex.o"
"f103_moduel\stm32f1xx_hal.o"
"f103_moduel\stm32f1xx_hal_rcc.o"
"f103_moduel\stm32f1xx_hal_rcc_ex.o"
"f103_moduel\stm32f1xx_hal_gpio.o"
"f103_moduel\stm32f1xx_hal_dma.o"
"f103_moduel\stm32f1xx_hal_cortex.o"
"f103_moduel\stm32f1xx_hal_pwr.o"
"f103_moduel\stm32f1xx_hal_flash.o"
"f103_moduel\stm32f1xx_hal_flash_ex.o"
"f103_moduel\stm32f1xx_hal_exti.o"
"f103_moduel\stm32f1xx_hal_tim.o"
"f103_moduel\stm32f1xx_hal_tim_ex.o"
"f103_moduel\stm32f1xx_hal_uart.o"
"f103_moduel\system_stm32f1xx.o"
"f103_moduel\driver_usart.o"
"f103_moduel\ring_buffer.o"
"f103_moduel\driver_key.o"
"f103_moduel\croutine.o"
"f103_moduel\event_groups.o"
"f103_moduel\list.o"
"f103_moduel\queue.o"
"f103_moduel\stream_buffer.o"
"f103_moduel\tasks.o"
"f103_moduel\timers.o"
"f103_moduel\cmsis_os2.o"
"f103_moduel\heap_4.o"
"f103_moduel\port.o"
--library_type=microlib --strict --scatter "F103_Moduel\F103_Moduel.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "F103_Moduel.map" -o F103_Moduel\F103_Moduel.axf