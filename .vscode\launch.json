{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Desktop/01_FreeRTOS_stacktrace/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang", "program": "c:/Users/<USER>/Desktop/01_FreeRTOS_stacktrace/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}