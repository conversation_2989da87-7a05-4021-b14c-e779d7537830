Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to driver_usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f103xe.o(RESET) refers to driver_key.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.TIM8_UP_IRQHandler) for TIM8_UP_IRQHandler
    startup_stm32f103xe.o(.text) refers to stm32f1xx_it.o(i.rt_hw_hard_fault_exception) for rt_hw_hard_fault_exception
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.A) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.A) refers to main.o(i.B) for B
    main.o(i.B) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.B) refers to main.o(i.C) for C
    main.o(i.D) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.D) refers to main.o(i.C) for C
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.TestDebug) refers to main.o(i.A) for A
    main.o(i.TestDebug) refers to main.o(i.D) for D
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to driver_key.o(i.KEY_GPIO_ReInit) for KEY_GPIO_ReInit
    main.o(i.main) refers to ring_buffer.o(i.ring_buffer_init) for ring_buffer_init
    main.o(i.main) refers to driver_usart.o(i.EnableDebugIRQ) for EnableDebugIRQ
    main.o(i.main) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.main) refers to main.o(i.TestDebug) for TestDebug
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    main.o(i.main) refers to main.o(.bss) for test_buffer
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for defaultTask_attributes
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDefaultTask) for StartDefaultTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for defaultTaskHandle
    freertos.o(i.StartDefaultTask) refers to printf8.o(i.__0printf$8) for __2printf
    freertos.o(i.StartDefaultTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART3_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for huart3
    stm32f1xx_it.o(i.TIM8_UP_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM8_UP_IRQHandler) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim8
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f1xx_it.o(i.rt_hw_hard_fault_exception) refers to printf8.o(i.__0printf$8) for __2printf
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim8
    stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim8
    stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim8
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to driver_key.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to driver_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to driver_usart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to driver_usart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to driver_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    driver_usart.o(i.DisableDebugIRQ) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    driver_usart.o(i.DisableDebugIRQ) refers to usart.o(.bss) for huart1
    driver_usart.o(i.EnableDebugIRQ) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    driver_usart.o(i.EnableDebugIRQ) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    driver_usart.o(i.EnableDebugIRQ) refers to usart.o(.bss) for huart1
    driver_usart.o(i.HAL_UART_RxCpltCallback) refers to driver_usart.o(.data) for rxcplt_flag
    driver_usart.o(i.HAL_UART_TxCpltCallback) refers to driver_usart.o(.data) for txcplt_flag
    driver_usart.o(i.USART1_IRQHandler) refers to ring_buffer.o(i.ring_buffer_write) for ring_buffer_write
    driver_usart.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    driver_usart.o(i.USART1_IRQHandler) refers to main.o(.bss) for test_buffer
    driver_usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    driver_usart.o(i.fgetc) refers to ring_buffer.o(i.ring_buffer_read) for ring_buffer_read
    driver_usart.o(i.fgetc) refers to main.o(.bss) for test_buffer
    driver_key.o(i.EXTI15_10_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    driver_key.o(i.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    driver_key.o(i.HAL_GPIO_EXTI_Callback) refers to driver_key.o(.data) for key1_val
    driver_key.o(i.KEY1_Value) refers to driver_key.o(.data) for key1_val
    driver_key.o(i.KEY2_Value) refers to driver_key.o(.data) for key2_val
    driver_key.o(i.KEY_GPIO_ReInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    driver_key.o(i.KEY_GPIO_ReInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    driver_key.o(i.KEY_GPIO_ReInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.prvCopyDataFromQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvCopyDataToQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvIsQueueFull) for prvIsQueueFull
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvGetDisinheritPriorityAfterTimeout) for prvGetDisinheritPriorityAfterTimeout
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memseta.o(.text) for memset
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for xTickCount
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(i.prvInitialiseTaskLists) for prvInitialiseTaskLists
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.bss) for xTasksWaitingTermination
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseNewTask) refers to memseta.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvInitialiseTaskLists) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for xTasksWaitingTermination
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for xYieldPending
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for xIdleTaskHandle
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for uxTopReadyPriority
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for xPendingReadyList
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for xTimerQueue
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for xActiveTimerList1
    timers.o(i.prvGetNextExpireTime) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for pxOverflowTimerList
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for xTimerQueue
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for pxOverflowTimerList
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for xLastTime
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvTimerTask) refers to timers.o(i.prvGetNextExpireTime) for prvGetNextExpireTime
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for xTimerTaskHandle
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for xTimerQueue
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osDelay) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osDelay) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelay) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osDelayUntil) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osDelayUntil) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osDelayUntil) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osDelayUntil) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsClear) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsClear) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsClear) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsClear) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsDelete) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsDelete) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsDelete) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsDelete) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsGet) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsGet) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsGet) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsGet) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsSet) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsSet) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsSet) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsSet) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osEventFlagsWait) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osEventFlagsWait) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osEventFlagsWait) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osEventFlagsWait) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelGetInfo) refers to memcpya.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelGetTickCount) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelGetTickCount) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetTickCount) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelLock) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelLock) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelLock) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelLock) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelRestoreLock) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelRestoreLock) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelRestoreLock) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osKernelUnlock) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osKernelUnlock) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osKernelUnlock) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelUnlock) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueDelete) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueDelete) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueDelete) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueDelete) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueGet) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueGet) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueGet) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGet) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueGetCount) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueGetCount) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetCount) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueueNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueuePut) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueuePut) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueuePut) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueuePut) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMessageQueueReset) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMessageQueueReset) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMessageQueueReset) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMessageQueueReset) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMutexAcquire) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMutexAcquire) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMutexAcquire) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexAcquire) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMutexDelete) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMutexDelete) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMutexDelete) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexDelete) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMutexGetOwner) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMutexGetOwner) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMutexGetOwner) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexGetOwner) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMutexNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMutexNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMutexNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osMutexRelease) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osMutexRelease) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osMutexRelease) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMutexRelease) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osSemaphoreAcquire) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osSemaphoreAcquire) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreAcquire) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osSemaphoreDelete) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osSemaphoreDelete) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osSemaphoreDelete) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreDelete) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osSemaphoreGetCount) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osSemaphoreGetCount) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreGetCount) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osSemaphoreNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osSemaphoreNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osSemaphoreNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osSemaphoreRelease) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osSemaphoreRelease) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osSemaphoreRelease) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreRelease) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadEnumerate) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadEnumerate) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadEnumerate) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadEnumerate) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadFlagsClear) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadFlagsClear) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsClear) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadFlagsGet) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadFlagsGet) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadFlagsGet) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadFlagsSet) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadFlagsSet) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadFlagsSet) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadFlagsWait) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadFlagsWait) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadFlagsWait) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadFlagsWait) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetCount) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetCount) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetCount) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetCount) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetId) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetId) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetId) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetId) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetName) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetName) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetName) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetName) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetPriority) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetPriority) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetPriority) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetPriority) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetStackSpace) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetStackSpace) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetStackSpace) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetStackSpace) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadGetState) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadGetState) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadGetState) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadGetState) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadResume) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadResume) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadResume) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadResume) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadSetPriority) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadSetPriority) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadSetPriority) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSetPriority) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadSuspend) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadSuspend) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadSuspend) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadSuspend) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadTerminate) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadTerminate) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadTerminate) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadTerminate) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osThreadYield) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osThreadYield) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osThreadYield) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osThreadYield) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerDelete) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerDelete) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerDelete) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerDelete) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerGetName) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerGetName) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerGetName) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerGetName) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerIsRunning) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerIsRunning) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerIsRunning) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerIsRunning) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerStart) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerStart) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStart) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.osTimerStop) refers to cmsis_os2.o(i.__get_IPSR) for __get_IPSR
    cmsis_os2.o(i.osTimerStop) refers to cmsis_os2.o(i.__get_PRIMASK) for __get_PRIMASK
    cmsis_os2.o(i.osTimerStop) refers to cmsis_os2.o(i.__get_BASEPRI) for __get_BASEPRI
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to cmsis_os2.o(.data) for KernelState
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for Idle_TCB
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for Timer_TCB
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for ucHeap
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for xStart
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for xStart
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for pxEnd
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for xBlockAllocatedBit
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for xFreeBytesRemaining
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for xMinimumEverFreeBytesRemaining
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.SysTick_Handler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    port.o(i.prvTaskExitError) refers to port.o(.data) for uxCriticalNesting
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortEnterCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortExitCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for ucMaxSysCallPriority
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for ucMaxSysCallPriority
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to driver_usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to driver_usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to driver_usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to driver_usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to driver_usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to driver_usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to driver_usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to driver_usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to driver_usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to driver_usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to driver_usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to driver_usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to driver_usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to driver_usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to driver_usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to driver_usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to driver_usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to driver_usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to driver_usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to driver_usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to driver_usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to driver_usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to driver_usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to driver_usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to driver_usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to driver_usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to driver_usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to driver_usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to driver_usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to driver_usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to driver_usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to driver_usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to driver_usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to driver_usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to driver_usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to driver_usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to driver_usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to driver_usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to driver_usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to driver_usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to driver_usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to driver_usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to driver_usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to driver_usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xe.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (96 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick), (24 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick), (24 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (24 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (20 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_Delay), (40 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_InitTick), (76 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (272 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (200 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (100 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (84 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (308 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (304 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (340 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (44 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (920 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (140 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1620 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (156 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config), (52 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (32 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (132 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (112 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (384 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (176 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (124 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (232 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (236 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (76 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (124 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (96 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (160 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (132 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (224 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (264 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (270 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (460 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (460 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (564 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (212 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (576 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (368 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (238 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (214 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (548 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (320 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (268 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (286 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (252 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (548 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (320 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (168 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (268 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (170 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (40 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (116 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (256 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (204 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (156 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (220 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (464 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (288 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (218 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (220 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (464 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (288 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (218 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (142 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (154 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (320 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (188 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (122 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (88 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (108 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (126 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (102 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (220 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (88 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (80 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (70 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (108 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing driver_usart.o(.rev16_text), (4 bytes).
    Removing driver_usart.o(.revsh_text), (4 bytes).
    Removing driver_usart.o(.rrx_text), (6 bytes).
    Removing driver_usart.o(i.DisableDebugIRQ), (32 bytes).
    Removing driver_usart.o(i.fgetc), (32 bytes).
    Removing ring_buffer.o(.rev16_text), (4 bytes).
    Removing ring_buffer.o(.revsh_text), (4 bytes).
    Removing ring_buffer.o(.rrx_text), (6 bytes).
    Removing ring_buffer.o(i.ring_buffer_read), (46 bytes).
    Removing driver_key.o(.rev16_text), (4 bytes).
    Removing driver_key.o(.revsh_text), (4 bytes).
    Removing driver_key.o(.rrx_text), (6 bytes).
    Removing driver_key.o(i.KEY1_Value), (12 bytes).
    Removing driver_key.o(i.KEY2_Value), (12 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (30 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (14 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (16 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (86 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (16 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (92 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (30 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (90 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (44 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (206 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (36 bytes).
    Removing event_groups.o(i.xEventGroupSync), (272 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (344 bytes).
    Removing queue.o(i.pcQueueGetName), (44 bytes).
    Removing queue.o(i.prvGetDisinheritPriorityAfterTimeout), (20 bytes).
    Removing queue.o(i.prvInitialiseMutex), (28 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (8 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (6 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (42 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (30 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (48 bytes).
    Removing queue.o(i.vQueueDelete), (50 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (48 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphore), (92 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphoreStatic), (100 bytes).
    Removing queue.o(i.xQueueCreateMutex), (34 bytes).
    Removing queue.o(i.xQueueCreateMutexStatic), (42 bytes).
    Removing queue.o(i.xQueueGenericCreate), (94 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (26 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (38 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (224 bytes).
    Removing queue.o(i.xQueueGiveMutexRecursive), (72 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (38 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (42 bytes).
    Removing queue.o(i.xQueuePeek), (368 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (176 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (202 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (440 bytes).
    Removing queue.o(i.xQueueTakeMutexRecursive), (78 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (24 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (96 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (202 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (72 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (186 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (92 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (10 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (6 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (56 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (42 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (114 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (222 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (46 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (64 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (254 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (102 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (184 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (86 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (274 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (102 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (184 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (50 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (54 bytes).
    Removing tasks.o(i.eTaskGetState), (132 bytes).
    Removing tasks.o(i.pcTaskGetName), (48 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (106 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (22 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (88 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (32 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (116 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (40 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (176 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (14 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (36 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (68 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (28 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (212 bytes).
    Removing tasks.o(i.vTaskDelete), (200 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (36 bytes).
    Removing tasks.o(i.vTaskGetInfo), (132 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (240 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (108 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (208 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (256 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (160 bytes).
    Removing tasks.o(i.vTaskResume), (144 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (60 bytes).
    Removing tasks.o(i.vTaskSuspend), (200 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (256 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (320 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (20 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (160 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (164 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (176 bytes).
    Removing timers.o(i.pcTimerGetName), (32 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (78 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (44 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (6 bytes).
    Removing timers.o(i.vTimerSetTimerID), (44 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (54 bytes).
    Removing timers.o(i.xTimerCreateStatic), (112 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (34 bytes).
    Removing timers.o(i.xTimerGetPeriod), (32 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (48 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (56 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (80 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (52 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (22 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (64 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (96 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (64 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (68 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (96 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (116 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (164 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (64 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (52 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (52 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (80 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (112 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (92 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (68 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGet), (152 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (14 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (64 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (14 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (100 bytes).
    Removing cmsis_os2.o(i.osMessageQueueNew), (184 bytes).
    Removing cmsis_os2.o(i.osMessageQueuePut), (156 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (64 bytes).
    Removing cmsis_os2.o(i.osMutexAcquire), (124 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (72 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (56 bytes).
    Removing cmsis_os2.o(i.osMutexNew), (208 bytes).
    Removing cmsis_os2.o(i.osMutexRelease), (104 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (132 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (68 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (64 bytes).
    Removing cmsis_os2.o(i.osSemaphoreNew), (236 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (116 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (132 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (10 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (112 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (72 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (152 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (204 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (48 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (48 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (56 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (56 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSize), (6 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (56 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (96 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (64 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (80 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (64 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (84 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (64 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (96 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (56 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (56 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (204 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (84 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (96 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (48 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

594 unused section(s) (total 53196 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\ModuleDrivers\driver_key.c            0x00000000   Number         0  driver_key.o ABSOLUTE
    ..\ModuleDrivers\driver_usart.c          0x00000000   Number         0  driver_usart.o ABSOLUTE
    ..\ModuleDrivers\ring_buffer.c           0x00000000   Number         0  ring_buffer.o ABSOLUTE
    ..\\ModuleDrivers\\driver_key.c          0x00000000   Number         0  driver_key.o ABSOLUTE
    ..\\ModuleDrivers\\driver_usart.c        0x00000000   Number         0  driver_usart.o ABSOLUTE
    ..\\ModuleDrivers\\ring_buffer.c         0x00000000   Number         0  ring_buffer.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x08000148   Section      150  port.o(.emb_text)
    .text                                    0x080001e0   Section       88  startup_stm32f103xe.o(.text)
    .text                                    0x08000238   Section        0  llushr.o(.text)
    .text                                    0x08000258   Section        0  memcpya.o(.text)
    .text                                    0x0800027c   Section        0  memseta.o(.text)
    .text                                    0x080002a0   Section        0  uldiv.o(.text)
    .text                                    0x08000304   Section       36  init.o(.text)
    .text                                    0x08000328   Section        0  llshl.o(.text)
    i.A                                      0x08000348   Section        0  main.o(i.A)
    A                                        0x08000349   Thumb Code    20  main.o(i.A)
    i.B                                      0x08000374   Section        0  main.o(i.B)
    B                                        0x08000375   Thumb Code    22  main.o(i.B)
    i.BusFault_Handler                       0x080003a4   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.C                                      0x080003a8   Section        0  main.o(i.C)
    C                                        0x080003a9   Thumb Code    10  main.o(i.C)
    i.D                                      0x080003b4   Section        0  main.o(i.D)
    D                                        0x080003b5   Thumb Code    22  main.o(i.D)
    i.DebugMon_Handler                       0x080003e4   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.EXTI15_10_IRQHandler                   0x080003e6   Section        0  driver_key.o(i.EXTI15_10_IRQHandler)
    i.EnableDebugIRQ                         0x080003fc   Section        0  driver_usart.o(i.EnableDebugIRQ)
    i.Error_Handler                          0x08000424   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x0800042a   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000480   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_GPIO_EXTI_Callback                 0x08000628   Section        0  driver_key.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x08000664   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08000680   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080009c0   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080009d0   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080009dc   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080009e8   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000a00   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000a28   Section        0  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000ab0   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000b10   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000b30   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000bac   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000bd4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08000d60   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08000da4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08000db0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000dd0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000df0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000e84   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_TIMEx_BreakCallback                0x080012e8   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080012ea   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x080012ec   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001352   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001354   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x080013e4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080013e6   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x0800157c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800157e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08001580   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001598   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x0800159a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x0800159c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080015a0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080017ec   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001860   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08001984   Section        0  driver_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x0800199c   Section        0  driver_usart.o(i.HAL_UART_TxCpltCallback)
    i.KEY_GPIO_ReInit                        0x080019b4   Section        0  driver_key.o(i.KEY_GPIO_ReInit)
    i.MX_FREERTOS_Init                       0x08001a3c   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x08001a5c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x08001b7c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART3_UART_Init                    0x08001bb4   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08001bec   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001bf0   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.RCC_Delay                              0x08001bf4   Section        0  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    RCC_Delay                                0x08001bf5   Thumb Code    36  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    i.StartDefaultTask                       0x08001c1c   Section        0  freertos.o(i.StartDefaultTask)
    i.SysTick_Handler                        0x08001c40   Section        0  port.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001c74   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001cda   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM8_UP_IRQHandler                     0x08001cdc   Section        0  stm32f1xx_it.o(i.TIM8_UP_IRQHandler)
    i.TIM_Base_SetConfig                     0x08001cec   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TestDebug                              0x08001d88   Section        0  main.o(i.TestDebug)
    i.UART_DMAAbortOnError                   0x08001da4   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08001da5   Thumb Code    20  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08001db8   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08001db9   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08001dee   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08001def   Thumb Code    32  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x08001e0e   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08001e0f   Thumb Code   228  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08001ef4   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08001ef5   Thumb Code   248  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Transmit_IT                       0x08001ff0   Section        0  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08001ff1   Thumb Code    96  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x08002050   Section        0  driver_usart.o(i.USART1_IRQHandler)
    i.USART3_IRQHandler                      0x08002084   Section        0  stm32f1xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08002094   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__0printf$8                            0x08002098   Section        0  printf8.o(i.__0printf$8)
    i.__NVIC_GetPriorityGrouping             0x080020b8   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x080020b9   Thumb Code    10  stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x080020c8   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080020c9   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__get_BASEPRI                          0x080020f0   Section        0  cmsis_os2.o(i.__get_BASEPRI)
    __get_BASEPRI                            0x080020f1   Thumb Code     6  cmsis_os2.o(i.__get_BASEPRI)
    i.__get_IPSR                             0x080020f6   Section        0  cmsis_os2.o(i.__get_IPSR)
    __get_IPSR                               0x080020f7   Thumb Code     6  cmsis_os2.o(i.__get_IPSR)
    i.__get_PRIMASK                          0x080020fc   Section        0  cmsis_os2.o(i.__get_PRIMASK)
    __get_PRIMASK                            0x080020fd   Thumb Code     6  cmsis_os2.o(i.__get_PRIMASK)
    i.__scatterload_copy                     0x08002102   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002110   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002112   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08002120   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08002121   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x08002524   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x08002525   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002548   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002549   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i.fputc                                  0x08002578   Section        0  driver_usart.o(i.fputc)
    i.main                                   0x08002594   Section        0  main.o(i.main)
    i.osDelay                                0x080025e4   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x0800261c   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x0800265c   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osThreadNew                            0x080026a0   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x0800277c   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x0800277d   Thumb Code   100  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x080027f8   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x080027f9   Thumb Code   154  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x080028b0   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x080028b1   Thumb Code    78  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCheckTasksWaitingTermination        0x08002924   Section        0  tasks.o(i.prvCheckTasksWaitingTermination)
    prvCheckTasksWaitingTermination          0x08002925   Thumb Code    60  tasks.o(i.prvCheckTasksWaitingTermination)
    i.prvCopyDataFromQueue                   0x0800296c   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x0800296d   Thumb Code    42  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x08002996   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x08002997   Thumb Code   124  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x08002a12   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08002a13   Thumb Code    80  tasks.o(i.prvDeleteTCB)
    i.prvGetNextExpireTime                   0x08002a64   Section        0  timers.o(i.prvGetNextExpireTime)
    prvGetNextExpireTime                     0x08002a65   Thumb Code    36  timers.o(i.prvGetNextExpireTime)
    i.prvHeapInit                            0x08002a8c   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08002a8d   Thumb Code    98  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08002b08   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08002b09   Thumb Code    32  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x08002b30   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08002b31   Thumb Code    42  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08002b5a   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08002b5b   Thumb Code   180  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseTaskLists                 0x08002c10   Section        0  tasks.o(i.prvInitialiseTaskLists)
    prvInitialiseTaskLists                   0x08002c11   Thumb Code    70  tasks.o(i.prvInitialiseTaskLists)
    i.prvInsertBlockIntoFreeList             0x08002c78   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08002c79   Thumb Code    96  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08002ce0   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08002ce1   Thumb Code    80  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08002d38   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08002d39   Thumb Code    26  queue.o(i.prvIsQueueEmpty)
    i.prvIsQueueFull                         0x08002d52   Section        0  queue.o(i.prvIsQueueFull)
    prvIsQueueFull                           0x08002d53   Thumb Code    30  queue.o(i.prvIsQueueFull)
    i.prvProcessExpiredTimer                 0x08002d70   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x08002d71   Thumb Code    94  timers.o(i.prvProcessExpiredTimer)
    i.prvProcessReceivedCommands             0x08002dd4   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08002dd5   Thumb Code   290  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08002efc   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08002efd   Thumb Code   102  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08002f70   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08002f71   Thumb Code    42  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08002fa4   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08002fa5   Thumb Code    40  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08002fd0   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08002fd1   Thumb Code   150  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08003070   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08003071   Thumb Code    60  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x080030b0   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x080030b1   Thumb Code    26  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x080030ca   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x080030cb   Thumb Code   126  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08003148   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08003284   Section        0  port.o(i.pxPortInitialiseStack)
    i.ring_buffer_init                       0x080032a8   Section        0  ring_buffer.o(i.ring_buffer_init)
    i.ring_buffer_write                      0x080032b4   Section        0  ring_buffer.o(i.ring_buffer_write)
    i.rt_hw_hard_fault_exception             0x080032d4   Section        0  stm32f1xx_it.o(i.rt_hw_hard_fault_exception)
    i.uxListRemove                           0x080034ac   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x080034d4   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x080034ec   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x08003504   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x0800351e   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08003524   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08003558   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08003570   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x080035c4   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08003600   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08003694   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x080036bc   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x08003734   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x08003760   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x080037ac   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08003808   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08003820   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x0800382c   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08003864   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x080038a4   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x0800395c   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x0800396c   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x080039fc   Section        0  port.o(i.xPortStartScheduler)
    i.xQueueGenericCreateStatic              0x08003af4   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x08003bd4   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x08003c6c   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08003e14   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueReceive                          0x08003f0c   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x08004074   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08004100   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08004164   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetSchedulerState                 0x0800421c   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x0800423c   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08004248   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x0800438c   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskRemoveFromEventList               0x08004430   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x080044c8   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x080045cc   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08004644   Section        0  timers.o(i.xTimerGenericCommand)
    .constdata                               0x080046c8   Section       36  freertos.o(.constdata)
    .constdata                               0x080046ec   Section       24  system_stm32f1xx.o(.constdata)
    .conststring                             0x08004704   Section       12  freertos.o(.conststring)
    .data                                    0x20000000   Section        4  freertos.o(.data)
    .data                                    0x20000004   Section        9  stm32f1xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000014   Section        3  driver_usart.o(.data)
    txcplt_flag                              0x20000014   Data           1  driver_usart.o(.data)
    rxcplt_flag                              0x20000015   Data           1  driver_usart.o(.data)
    rx_data                                  0x20000016   Data           1  driver_usart.o(.data)
    .data                                    0x20000017   Section        2  driver_key.o(.data)
    key1_val                                 0x20000017   Data           1  driver_key.o(.data)
    key2_val                                 0x20000018   Data           1  driver_key.o(.data)
    .data                                    0x2000001c   Section       60  tasks.o(.data)
    pxDelayedTaskList                        0x20000020   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000024   Data           4  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000028   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x2000002c   Data           4  tasks.o(.data)
    xTickCount                               0x20000030   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000034   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000038   Data           4  tasks.o(.data)
    uxPendedTicks                            0x2000003c   Data           4  tasks.o(.data)
    xYieldPending                            0x20000040   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000044   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000048   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x2000004c   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000050   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000054   Data           4  tasks.o(.data)
    .data                                    0x20000058   Section       20  timers.o(.data)
    pxCurrentTimerList                       0x20000058   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x2000005c   Data           4  timers.o(.data)
    xTimerQueue                              0x20000060   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x20000064   Data           4  timers.o(.data)
    xLastTime                                0x20000068   Data           4  timers.o(.data)
    .data                                    0x2000006c   Section        4  cmsis_os2.o(.data)
    KernelState                              0x2000006c   Data           4  cmsis_os2.o(.data)
    .data                                    0x20000070   Section       24  heap_4.o(.data)
    xStart                                   0x20000070   Data           8  heap_4.o(.data)
    pxEnd                                    0x20000078   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x2000007c   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000080   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x20000084   Data           4  heap_4.o(.data)
    .data                                    0x20000088   Section       12  port.o(.data)
    uxCriticalNesting                        0x20000088   Data           4  port.o(.data)
    ucMaxSysCallPriority                     0x2000008c   Data           1  port.o(.data)
    ulMaxPRIGROUPValue                       0x20000090   Data           4  port.o(.data)
    .data                                    0x20000094   Section        4  stdout.o(.data)
    .bss                                     0x20000098   Section     1032  main.o(.bss)
    .bss                                     0x200004a0   Section      136  usart.o(.bss)
    .bss                                     0x20000528   Section       72  stm32f1xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20000570   Section       64  queue.o(.bss)
    .bss                                     0x200005b0   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x200005b0   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20000a10   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20000a24   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20000a38   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20000a4c   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20000a60   Data          20  tasks.o(.bss)
    .bss                                     0x20000a74   Section      280  timers.o(.bss)
    xActiveTimerList1                        0x20000a74   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20000a88   Data          20  timers.o(.bss)
    xStaticTimerQueue                        0x20000a9c   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x20000aec   Data         160  timers.o(.bss)
    .bss                                     0x20000b8c   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x20000b8c   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x20000be8   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x20000de8   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x20000e44   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x20001244   Section     3072  heap_4.o(.bss)
    ucHeap                                   0x20001244   Data        3072  heap_4.o(.bss)
    STACK                                    0x20001e48   Section     1024  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    SVC_Handler                              0x08000149   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x0800016d   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x0800018d   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x080001d9   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x080001e1   Thumb Code     8  startup_stm32f103xe.o(.text)
    HardFault_Handler                        0x080001eb   Thumb Code    52  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI0_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI1_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI2_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI4_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_EV_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_UP_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x0800022d   Thumb Code     0  startup_stm32f103xe.o(.text)
    __aeabi_llsr                             0x08000239   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000239   Thumb Code     0  llushr.o(.text)
    __aeabi_memcpy                           0x08000259   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000259   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000259   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800027d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800028b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800028b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800028b   Thumb Code     0  memseta.o(.text)
    memset                                   0x0800028f   Thumb Code    18  memseta.o(.text)
    __aeabi_uldivmod                         0x080002a1   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x08000305   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000305   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000329   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000329   Thumb Code     0  llshl.o(.text)
    BusFault_Handler                         0x080003a5   Thumb Code     4  stm32f1xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080003e5   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    EXTI15_10_IRQHandler                     0x080003e7   Thumb Code    20  driver_key.o(i.EXTI15_10_IRQHandler)
    EnableDebugIRQ                           0x080003fd   Thumb Code    36  driver_usart.o(i.EnableDebugIRQ)
    Error_Handler                            0x08000425   Thumb Code     6  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x0800042b   Thumb Code    86  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000481   Thumb Code   414  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_GPIO_EXTI_Callback                   0x08000629   Thumb Code    48  driver_key.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08000665   Thumb Code    24  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08000681   Thumb Code   792  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080009c1   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080009d1   Thumb Code    12  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080009dd   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080009e9   Thumb Code    16  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000a01   Thumb Code    34  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000a29   Thumb Code   118  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000ab1   Thumb Code    86  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000b11   Thumb Code    32  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000b31   Thumb Code   124  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000bad   Thumb Code    32  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000bd5   Thumb Code   376  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08000d61   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08000da5   Thumb Code     6  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08000db1   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000dd1   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000df1   Thumb Code   116  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000e85   Thumb Code  1114  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_TIMEx_BreakCallback                  0x080012e9   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080012eb   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x080012ed   Thumb Code   102  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001353   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001355   Thumb Code   122  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x080013e5   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080013e7   Thumb Code   406  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x0800157d   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800157f   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08001581   Thumb Code    18  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001599   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x0800159b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x0800159d   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080015a1   Thumb Code   582  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080017ed   Thumb Code   114  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001861   Thumb Code   266  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08001985   Thumb Code    16  driver_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x0800199d   Thumb Code    16  driver_usart.o(i.HAL_UART_TxCpltCallback)
    KEY_GPIO_ReInit                          0x080019b5   Thumb Code   122  driver_key.o(i.KEY_GPIO_ReInit)
    MX_FREERTOS_Init                         0x08001a3d   Thumb Code    18  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x08001a5d   Thumb Code   272  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08001b7d   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MX_USART3_UART_Init                      0x08001bb5   Thumb Code    46  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08001bed   Thumb Code     4  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001bf1   Thumb Code     4  stm32f1xx_it.o(i.NMI_Handler)
    StartDefaultTask                         0x08001c1d   Thumb Code    16  freertos.o(i.StartDefaultTask)
    SysTick_Handler                          0x08001c41   Thumb Code    46  port.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001c75   Thumb Code   102  main.o(i.SystemClock_Config)
    SystemInit                               0x08001cdb   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM8_UP_IRQHandler                       0x08001cdd   Thumb Code    10  stm32f1xx_it.o(i.TIM8_UP_IRQHandler)
    TIM_Base_SetConfig                       0x08001ced   Thumb Code   134  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TestDebug                                0x08001d89   Thumb Code    22  main.o(i.TestDebug)
    USART1_IRQHandler                        0x08002051   Thumb Code    38  driver_usart.o(i.USART1_IRQHandler)
    USART3_IRQHandler                        0x08002085   Thumb Code    10  stm32f1xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08002095   Thumb Code     4  stm32f1xx_it.o(i.UsageFault_Handler)
    __0printf$8                              0x08002099   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08002099   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08002099   Thumb Code     0  printf8.o(i.__0printf$8)
    __scatterload_copy                       0x08002103   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002111   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002113   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x08002579   Thumb Code    24  driver_usart.o(i.fputc)
    main                                     0x08002595   Thumb Code    60  main.o(i.main)
    osDelay                                  0x080025e5   Thumb Code    50  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x0800261d   Thumb Code    58  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x0800265d   Thumb Code    64  cmsis_os2.o(i.osKernelStart)
    osThreadNew                              0x080026a1   Thumb Code   216  cmsis_os2.o(i.osThreadNew)
    pvPortMalloc                             0x08003149   Thumb Code   296  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08003285   Thumb Code    30  port.o(i.pxPortInitialiseStack)
    ring_buffer_init                         0x080032a9   Thumb Code    12  ring_buffer.o(i.ring_buffer_init)
    ring_buffer_write                        0x080032b5   Thumb Code    30  ring_buffer.o(i.ring_buffer_write)
    rt_hw_hard_fault_exception               0x080032d5   Thumb Code   194  stm32f1xx_it.o(i.rt_hw_hard_fault_exception)
    uxListRemove                             0x080034ad   Thumb Code    40  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x080034d5   Thumb Code    14  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x080034ed   Thumb Code    16  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x08003505   Thumb Code    26  list.o(i.vListInitialise)
    vListInitialiseItem                      0x0800351f   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08003525   Thumb Code    52  list.o(i.vListInsert)
    vListInsertEnd                           0x08003559   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08003571   Thumb Code    76  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x080035c5   Thumb Code    56  port.o(i.vPortExitCritical)
    vPortFree                                0x08003601   Thumb Code   140  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08003695   Thumb Code    36  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x080036bd   Thumb Code   106  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x08003735   Thumb Code    38  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x08003761   Thumb Code    74  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x080037ad   Thumb Code    82  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08003809   Thumb Code    14  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08003821   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x0800382d   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08003865   Thumb Code    60  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x080038a5   Thumb Code   156  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x0800395d   Thumb Code    12  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x0800396d   Thumb Code   122  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x080039fd   Thumb Code   228  port.o(i.xPortStartScheduler)
    xQueueGenericCreateStatic                0x08003af5   Thumb Code   222  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x08003bd5   Thumb Code   146  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x08003c6d   Thumb Code   420  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08003e15   Thumb Code   248  queue.o(i.xQueueGenericSendFromISR)
    xQueueReceive                            0x08003f0d   Thumb Code   356  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x08004075   Thumb Code   132  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08004101   Thumb Code   100  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08004165   Thumb Code   184  tasks.o(i.xTaskCreateStatic)
    xTaskGetSchedulerState                   0x0800421d   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x0800423d   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08004249   Thumb Code   280  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x0800438d   Thumb Code   152  tasks.o(i.xTaskPriorityDisinherit)
    xTaskRemoveFromEventList                 0x08004431   Thumb Code   126  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x080044c9   Thumb Code   222  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x080045cd   Thumb Code   100  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08004645   Thumb Code   128  timers.o(i.xTimerGenericCommand)
    defaultTask_attributes                   0x080046c8   Data          36  freertos.o(.constdata)
    AHBPrescTable                            0x080046ec   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x080046fc   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x08004710   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08004730   Number         0  anon$$obj.o(Region$$Table)
    defaultTaskHandle                        0x20000000   Data           4  freertos.o(.data)
    uwTick                                   0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f1xx_hal.o(.data)
    uwTickFreq                               0x2000000c   Data           1  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f1xx.o(.data)
    pxCurrentTCB                             0x2000001c   Data           4  tasks.o(.data)
    __stdout                                 0x20000094   Data           4  stdout.o(.data)
    test_buffer                              0x20000098   Data        1032  main.o(.bss)
    huart1                                   0x200004a0   Data          68  usart.o(.bss)
    huart3                                   0x200004e4   Data          68  usart.o(.bss)
    htim8                                    0x20000528   Data          72  stm32f1xx_hal_timebase_tim.o(.bss)
    xQueueRegistry                           0x20000570   Data          64  queue.o(.bss)
    __initial_sp                             0x20002248   Data           0  startup_stm32f103xe.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000047c8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00004730, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO            3    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000000   Code   RO         4717  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         4991    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         4994    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4996    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4998    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         4999    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         5006    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO         5001    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO         5003    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO         4992    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x00000096   Code   RO         4640    .emb_text           port.o
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x00000058   Code   RO            4    .text               startup_stm32f103xe.o
    0x08000238   0x08000238   0x00000020   Code   RO         4720    .text               mc_w.l(llushr.o)
    0x08000258   0x08000258   0x00000024   Code   RO         4722    .text               mc_w.l(memcpya.o)
    0x0800027c   0x0800027c   0x00000024   Code   RO         4726    .text               mc_w.l(memseta.o)
    0x080002a0   0x080002a0   0x00000062   Code   RO         5010    .text               mc_w.l(uldiv.o)
    0x08000302   0x08000302   0x00000002   PAD
    0x08000304   0x08000304   0x00000024   Code   RO         5023    .text               mc_w.l(init.o)
    0x08000328   0x08000328   0x0000001e   Code   RO         5025    .text               mc_w.l(llshl.o)
    0x08000346   0x08000346   0x00000002   PAD
    0x08000348   0x08000348   0x0000002c   Code   RO           13    i.A                 main.o
    0x08000374   0x08000374   0x00000030   Code   RO           14    i.B                 main.o
    0x080003a4   0x080003a4   0x00000004   Code   RO          344    i.BusFault_Handler  stm32f1xx_it.o
    0x080003a8   0x080003a8   0x0000000a   Code   RO           15    i.C                 main.o
    0x080003b2   0x080003b2   0x00000002   PAD
    0x080003b4   0x080003b4   0x00000030   Code   RO           16    i.D                 main.o
    0x080003e4   0x080003e4   0x00000002   Code   RO          345    i.DebugMon_Handler  stm32f1xx_it.o
    0x080003e6   0x080003e6   0x00000014   Code   RO         3005    i.EXTI15_10_IRQHandler  driver_key.o
    0x080003fa   0x080003fa   0x00000002   PAD
    0x080003fc   0x080003fc   0x00000028   Code   RO         2908    i.EnableDebugIRQ    driver_usart.o
    0x08000424   0x08000424   0x00000006   Code   RO           17    i.Error_Handler     main.o
    0x0800042a   0x0800042a   0x00000056   Code   RO          897    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08000480   0x08000480   0x000001a8   Code   RO          898    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000628   0x08000628   0x0000003c   Code   RO         3006    i.HAL_GPIO_EXTI_Callback  driver_key.o
    0x08000664   0x08000664   0x0000001c   Code   RO          832    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x08000680   0x08000680   0x00000340   Code   RO          833    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080009c0   0x080009c0   0x00000010   Code   RO          835    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x080009d0   0x080009d0   0x0000000c   Code   RO          837    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080009dc   0x080009dc   0x0000000c   Code   RO          524    i.HAL_GetTick       stm32f1xx_hal.o
    0x080009e8   0x080009e8   0x00000018   Code   RO          530    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000a00   0x08000a00   0x00000028   Code   RO          531    i.HAL_Init          stm32f1xx_hal.o
    0x08000a28   0x08000a28   0x00000088   Code   RO          438    i.HAL_InitTick      stm32f1xx_hal_timebase_tim.o
    0x08000ab0   0x08000ab0   0x00000060   Code   RO          414    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000b10   0x08000b10   0x00000020   Code   RO          993    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08000b30   0x08000b30   0x0000007c   Code   RO          999    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08000bac   0x08000bac   0x00000028   Code   RO         1000    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000bd4   0x08000bd4   0x0000018c   Code   RO          688    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08000d60   0x08000d60   0x00000044   Code   RO          692    i.HAL_RCC_GetClockConfig  stm32f1xx_hal_rcc.o
    0x08000da4   0x08000da4   0x0000000c   Code   RO          693    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x08000db0   0x08000db0   0x00000020   Code   RO          695    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08000dd0   0x08000dd0   0x00000020   Code   RO          696    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08000df0   0x08000df0   0x00000094   Code   RO          697    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08000e84   0x08000e84   0x00000464   Code   RO          700    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x080012e8   0x080012e8   0x00000002   Code   RO         2236    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x080012ea   0x080012ea   0x00000002   Code   RO         2237    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x080012ec   0x080012ec   0x00000066   Code   RO         1521    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08001352   0x08001352   0x00000002   Code   RO         1523    i.HAL_TIM_Base_MspInit  stm32f1xx_hal_tim.o
    0x08001354   0x08001354   0x00000090   Code   RO         1526    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x080013e4   0x080013e4   0x00000002   Code   RO         1555    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x080013e6   0x080013e6   0x00000196   Code   RO         1569    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x0800157c   0x0800157c   0x00000002   Code   RO         1572    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x0800157e   0x0800157e   0x00000002   Code   RO         1599    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08001580   0x08001580   0x00000018   Code   RO           18    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08001598   0x08001598   0x00000002   Code   RO         1612    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x0800159a   0x0800159a   0x00000002   Code   RO         2514    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x0800159c   0x0800159c   0x00000002   Code   RO         2528    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800159e   0x0800159e   0x00000002   PAD
    0x080015a0   0x080015a0   0x0000024c   Code   RO         2531    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x080017ec   0x080017ec   0x00000072   Code   RO         2532    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x0800185e   0x0800185e   0x00000002   PAD
    0x08001860   0x08001860   0x00000124   Code   RO          297    i.HAL_UART_MspInit  usart.o
    0x08001984   0x08001984   0x00000018   Code   RO         2909    i.HAL_UART_RxCpltCallback  driver_usart.o
    0x0800199c   0x0800199c   0x00000018   Code   RO         2910    i.HAL_UART_TxCpltCallback  driver_usart.o
    0x080019b4   0x080019b4   0x00000088   Code   RO         3009    i.KEY_GPIO_ReInit   driver_key.o
    0x08001a3c   0x08001a3c   0x00000020   Code   RO          247    i.MX_FREERTOS_Init  freertos.o
    0x08001a5c   0x08001a5c   0x00000120   Code   RO          219    i.MX_GPIO_Init      gpio.o
    0x08001b7c   0x08001b7c   0x00000038   Code   RO          298    i.MX_USART1_UART_Init  usart.o
    0x08001bb4   0x08001bb4   0x00000038   Code   RO          299    i.MX_USART3_UART_Init  usart.o
    0x08001bec   0x08001bec   0x00000004   Code   RO          346    i.MemManage_Handler  stm32f1xx_it.o
    0x08001bf0   0x08001bf0   0x00000004   Code   RO          347    i.NMI_Handler       stm32f1xx_it.o
    0x08001bf4   0x08001bf4   0x00000028   Code   RO          701    i.RCC_Delay         stm32f1xx_hal_rcc.o
    0x08001c1c   0x08001c1c   0x00000024   Code   RO          248    i.StartDefaultTask  freertos.o
    0x08001c40   0x08001c40   0x00000034   Code   RO         4641    i.SysTick_Handler   port.o
    0x08001c74   0x08001c74   0x00000066   Code   RO           19    i.SystemClock_Config  main.o
    0x08001cda   0x08001cda   0x00000002   Code   RO         2871    i.SystemInit        system_stm32f1xx.o
    0x08001cdc   0x08001cdc   0x00000010   Code   RO          348    i.TIM8_UP_IRQHandler  stm32f1xx_it.o
    0x08001cec   0x08001cec   0x0000009c   Code   RO         1614    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08001d88   0x08001d88   0x0000001c   Code   RO           20    i.TestDebug         main.o
    0x08001da4   0x08001da4   0x00000014   Code   RO         2545    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08001db8   0x08001db8   0x00000036   Code   RO         2555    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08001dee   0x08001dee   0x00000020   Code   RO         2556    i.UART_EndTransmit_IT  stm32f1xx_hal_uart.o
    0x08001e0e   0x08001e0e   0x000000e4   Code   RO         2558    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08001ef2   0x08001ef2   0x00000002   PAD
    0x08001ef4   0x08001ef4   0x000000fc   Code   RO         2559    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08001ff0   0x08001ff0   0x00000060   Code   RO         2562    i.UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x08002050   0x08002050   0x00000034   Code   RO         2911    i.USART1_IRQHandler  driver_usart.o
    0x08002084   0x08002084   0x00000010   Code   RO          349    i.USART3_IRQHandler  stm32f1xx_it.o
    0x08002094   0x08002094   0x00000004   Code   RO          350    i.UsageFault_Handler  stm32f1xx_it.o
    0x08002098   0x08002098   0x00000020   Code   RO         4937    i.__0printf$8       mc_w.l(printf8.o)
    0x080020b8   0x080020b8   0x00000010   Code   RO         1006    i.__NVIC_GetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x080020c8   0x080020c8   0x00000028   Code   RO         1007    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080020f0   0x080020f0   0x00000006   Code   RO         4155    i.__get_BASEPRI     cmsis_os2.o
    0x080020f6   0x080020f6   0x00000006   Code   RO         4156    i.__get_IPSR        cmsis_os2.o
    0x080020fc   0x080020fc   0x00000006   Code   RO         4157    i.__get_PRIMASK     cmsis_os2.o
    0x08002102   0x08002102   0x0000000e   Code   RO         5033    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002110   0x08002110   0x00000002   Code   RO         5034    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002112   0x08002112   0x0000000e   Code   RO         5035    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002120   0x08002120   0x00000404   Code   RO         4944    i._printf_core      mc_w.l(printf8.o)
    0x08002524   0x08002524   0x00000024   Code   RO         4945    i._printf_post_padding  mc_w.l(printf8.o)
    0x08002548   0x08002548   0x0000002e   Code   RO         4946    i._printf_pre_padding  mc_w.l(printf8.o)
    0x08002576   0x08002576   0x00000002   PAD
    0x08002578   0x08002578   0x0000001c   Code   RO         2913    i.fputc             driver_usart.o
    0x08002594   0x08002594   0x00000050   Code   RO           21    i.main              main.o
    0x080025e4   0x080025e4   0x00000038   Code   RO         4158    i.osDelay           cmsis_os2.o
    0x0800261c   0x0800261c   0x00000040   Code   RO         4172    i.osKernelInitialize  cmsis_os2.o
    0x0800265c   0x0800265c   0x00000044   Code   RO         4175    i.osKernelStart     cmsis_os2.o
    0x080026a0   0x080026a0   0x000000dc   Code   RO         4209    i.osThreadNew       cmsis_os2.o
    0x0800277c   0x0800277c   0x0000007c   Code   RO         3630    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x080027f8   0x080027f8   0x000000b8   Code   RO         3631    i.prvAddNewTaskToReadyList  tasks.o
    0x080028b0   0x080028b0   0x00000074   Code   RO         3991    i.prvCheckForValidListAndQueue  timers.o
    0x08002924   0x08002924   0x00000048   Code   RO         3632    i.prvCheckTasksWaitingTermination  tasks.o
    0x0800296c   0x0800296c   0x0000002a   Code   RO         3223    i.prvCopyDataFromQueue  queue.o
    0x08002996   0x08002996   0x0000007c   Code   RO         3224    i.prvCopyDataToQueue  queue.o
    0x08002a12   0x08002a12   0x00000050   Code   RO         3633    i.prvDeleteTCB      tasks.o
    0x08002a62   0x08002a62   0x00000002   PAD
    0x08002a64   0x08002a64   0x00000028   Code   RO         3992    i.prvGetNextExpireTime  timers.o
    0x08002a8c   0x08002a8c   0x0000007c   Code   RO         4591    i.prvHeapInit       heap_4.o
    0x08002b08   0x08002b08   0x00000028   Code   RO         3634    i.prvIdleTask       tasks.o
    0x08002b30   0x08002b30   0x0000002a   Code   RO         3227    i.prvInitialiseNewQueue  queue.o
    0x08002b5a   0x08002b5a   0x000000b4   Code   RO         3635    i.prvInitialiseNewTask  tasks.o
    0x08002c0e   0x08002c0e   0x00000002   PAD
    0x08002c10   0x08002c10   0x00000068   Code   RO         3636    i.prvInitialiseTaskLists  tasks.o
    0x08002c78   0x08002c78   0x00000068   Code   RO         4592    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08002ce0   0x08002ce0   0x00000058   Code   RO         3994    i.prvInsertTimerInActiveList  timers.o
    0x08002d38   0x08002d38   0x0000001a   Code   RO         3228    i.prvIsQueueEmpty   queue.o
    0x08002d52   0x08002d52   0x0000001e   Code   RO         3229    i.prvIsQueueFull    queue.o
    0x08002d70   0x08002d70   0x00000064   Code   RO         3995    i.prvProcessExpiredTimer  timers.o
    0x08002dd4   0x08002dd4   0x00000128   Code   RO         3996    i.prvProcessReceivedCommands  timers.o
    0x08002efc   0x08002efc   0x00000074   Code   RO         3997    i.prvProcessTimerOrBlockTask  timers.o
    0x08002f70   0x08002f70   0x00000034   Code   RO         3638    i.prvResetNextTaskUnblockTime  tasks.o
    0x08002fa4   0x08002fa4   0x0000002c   Code   RO         3998    i.prvSampleTimeNow  timers.o
    0x08002fd0   0x08002fd0   0x000000a0   Code   RO         3999    i.prvSwitchTimerLists  timers.o
    0x08003070   0x08003070   0x00000040   Code   RO         4642    i.prvTaskExitError  port.o
    0x080030b0   0x080030b0   0x0000001a   Code   RO         4000    i.prvTimerTask      timers.o
    0x080030ca   0x080030ca   0x0000007e   Code   RO         3230    i.prvUnlockQueue    queue.o
    0x08003148   0x08003148   0x0000013c   Code   RO         4593    i.pvPortMalloc      heap_4.o
    0x08003284   0x08003284   0x00000024   Code   RO         4643    i.pxPortInitialiseStack  port.o
    0x080032a8   0x080032a8   0x0000000c   Code   RO         2969    i.ring_buffer_init  ring_buffer.o
    0x080032b4   0x080032b4   0x0000001e   Code   RO         2971    i.ring_buffer_write  ring_buffer.o
    0x080032d2   0x080032d2   0x00000002   PAD
    0x080032d4   0x080032d4   0x000001d8   Code   RO          351    i.rt_hw_hard_fault_exception  stm32f1xx_it.o
    0x080034ac   0x080034ac   0x00000028   Code   RO         3183    i.uxListRemove      list.o
    0x080034d4   0x080034d4   0x00000018   Code   RO         4221    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x080034ec   0x080034ec   0x00000018   Code   RO         4222    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x08003504   0x08003504   0x0000001a   Code   RO         3184    i.vListInitialise   list.o
    0x0800351e   0x0800351e   0x00000006   Code   RO         3185    i.vListInitialiseItem  list.o
    0x08003524   0x08003524   0x00000034   Code   RO         3186    i.vListInsert       list.o
    0x08003558   0x08003558   0x00000018   Code   RO         3187    i.vListInsertEnd    list.o
    0x08003570   0x08003570   0x00000054   Code   RO         4645    i.vPortEnterCritical  port.o
    0x080035c4   0x080035c4   0x0000003c   Code   RO         4646    i.vPortExitCritical  port.o
    0x08003600   0x08003600   0x00000094   Code   RO         4594    i.vPortFree         heap_4.o
    0x08003694   0x08003694   0x00000028   Code   RO         4647    i.vPortSetupTimerInterrupt  port.o
    0x080036bc   0x080036bc   0x00000078   Code   RO         4648    i.vPortValidateInterruptPriority  port.o
    0x08003734   0x08003734   0x0000002c   Code   RO         3236    i.vQueueAddToRegistry  queue.o
    0x08003760   0x08003760   0x0000004a   Code   RO         3240    i.vQueueWaitForMessageRestricted  queue.o
    0x080037aa   0x080037aa   0x00000002   PAD
    0x080037ac   0x080037ac   0x0000005c   Code   RO         3650    i.vTaskDelay        tasks.o
    0x08003808   0x08003808   0x00000018   Code   RO         3655    i.vTaskInternalSetTimeOutState  tasks.o
    0x08003820   0x08003820   0x0000000c   Code   RO         3656    i.vTaskMissedYield  tasks.o
    0x0800382c   0x0800382c   0x00000038   Code   RO         3658    i.vTaskPlaceOnEventList  tasks.o
    0x08003864   0x08003864   0x00000040   Code   RO         3659    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x080038a4   0x080038a4   0x000000b8   Code   RO         3667    i.vTaskStartScheduler  tasks.o
    0x0800395c   0x0800395c   0x00000010   Code   RO         3669    i.vTaskSuspendAll   tasks.o
    0x0800396c   0x0800396c   0x00000090   Code   RO         3670    i.vTaskSwitchContext  tasks.o
    0x080039fc   0x080039fc   0x000000f8   Code   RO         4649    i.xPortStartScheduler  port.o
    0x08003af4   0x08003af4   0x000000de   Code   RO         3246    i.xQueueGenericCreateStatic  queue.o
    0x08003bd2   0x08003bd2   0x00000002   PAD
    0x08003bd4   0x08003bd4   0x00000098   Code   RO         3247    i.xQueueGenericReset  queue.o
    0x08003c6c   0x08003c6c   0x000001a8   Code   RO         3248    i.xQueueGenericSend  queue.o
    0x08003e14   0x08003e14   0x000000f8   Code   RO         3249    i.xQueueGenericSendFromISR  queue.o
    0x08003f0c   0x08003f0c   0x00000168   Code   RO         3258    i.xQueueReceive     queue.o
    0x08004074   0x08004074   0x0000008c   Code   RO         3671    i.xTaskCheckForTimeOut  tasks.o
    0x08004100   0x08004100   0x00000064   Code   RO         3672    i.xTaskCreate       tasks.o
    0x08004164   0x08004164   0x000000b8   Code   RO         3673    i.xTaskCreateStatic  tasks.o
    0x0800421c   0x0800421c   0x00000020   Code   RO         3677    i.xTaskGetSchedulerState  tasks.o
    0x0800423c   0x0800423c   0x0000000c   Code   RO         3678    i.xTaskGetTickCount  tasks.o
    0x08004248   0x08004248   0x00000144   Code   RO         3680    i.xTaskIncrementTick  tasks.o
    0x0800438c   0x0800438c   0x000000a4   Code   RO         3683    i.xTaskPriorityDisinherit  tasks.o
    0x08004430   0x08004430   0x00000098   Code   RO         3685    i.xTaskRemoveFromEventList  tasks.o
    0x080044c8   0x080044c8   0x00000104   Code   RO         3686    i.xTaskResumeAll    tasks.o
    0x080045cc   0x080045cc   0x00000078   Code   RO         4007    i.xTimerCreateTimerTask  timers.o
    0x08004644   0x08004644   0x00000084   Code   RO         4008    i.xTimerGenericCommand  timers.o
    0x080046c8   0x080046c8   0x00000024   Data   RO          249    .constdata          freertos.o
    0x080046ec   0x080046ec   0x00000018   Data   RO         2872    .constdata          system_stm32f1xx.o
    0x08004704   0x08004704   0x0000000c   Data   RO          250    .conststring        freertos.o
    0x08004710   0x08004710   0x00000020   Data   RO         5031    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08004730, Size: 0x00002248, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08004730   0x00000004   Data   RW          251    .data               freertos.o
    0x20000004   0x08004734   0x00000009   Data   RW          538    .data               stm32f1xx_hal.o
    0x2000000d   0x0800473d   0x00000003   PAD
    0x20000010   0x08004740   0x00000004   Data   RW         2873    .data               system_stm32f1xx.o
    0x20000014   0x08004744   0x00000003   Data   RW         2914    .data               driver_usart.o
    0x20000017   0x08004747   0x00000002   Data   RW         3010    .data               driver_key.o
    0x20000019   0x08004749   0x00000003   PAD
    0x2000001c   0x0800474c   0x0000003c   Data   RW         3689    .data               tasks.o
    0x20000058   0x08004788   0x00000014   Data   RW         4016    .data               timers.o
    0x2000006c   0x0800479c   0x00000004   Data   RW         4224    .data               cmsis_os2.o
    0x20000070   0x080047a0   0x00000018   Data   RW         4599    .data               heap_4.o
    0x20000088   0x080047b8   0x0000000c   Data   RW         4650    .data               port.o
    0x20000094   0x080047c4   0x00000004   Data   RW         5007    .data               mc_w.l(stdout.o)
    0x20000098        -       0x00000408   Zero   RW           22    .bss                main.o
    0x200004a0        -       0x00000088   Zero   RW          300    .bss                usart.o
    0x20000528        -       0x00000048   Zero   RW          441    .bss                stm32f1xx_hal_timebase_tim.o
    0x20000570        -       0x00000040   Zero   RW         3262    .bss                queue.o
    0x200005b0        -       0x000004c4   Zero   RW         3688    .bss                tasks.o
    0x20000a74        -       0x00000118   Zero   RW         4015    .bss                timers.o
    0x20000b8c        -       0x000006b8   Zero   RW         4223    .bss                cmsis_os2.o
    0x20001244        -       0x00000c00   Zero   RW         4598    .bss                heap_4.o
    0x20001e44   0x080047c8   0x00000004   PAD
    0x20001e48        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       474         38          0          4       1720      14862   cmsis_os2.o
       216         26          0          2          0       2138   driver_key.o
       168         38          0          3          0       3197   driver_usart.o
         0          0          0          0          0      20164   event_groups.o
        68         34         48          4          0       4353   freertos.o
       288         16          0          0          0       1011   gpio.o
       692         62          0         24       3072       4615   heap_4.o
       148          0          0          0          0       3362   list.o
       390        108          0          0       1032     499910   main.o
       854         78          0         12          0      10777   port.o
      1914         20          0          0         64      16592   queue.o
        42          0          0          0          0       1375   ring_buffer.o
        88         10        304          0       1024        812   startup_stm32f103xe.o
        76         20          0          9          0       5186   stm32f1xx_hal.o
       252         22          0          0          0      28622   stm32f1xx_hal_cortex.o
       510         10          0          0          0       1859   stm32f1xx_hal_dma.o
       888         44          0          0          0       3878   stm32f1xx_hal_gpio.o
        96         10          0          0          0        874   stm32f1xx_hal_msp.o
      1852        120          0          0          0       6529   stm32f1xx_hal_rcc.o
       818         44          0          0          0       6038   stm32f1xx_hal_tim.o
         4          0          0          0          0       1485   stm32f1xx_hal_tim_ex.o
       136         18          0          0         72       1431   stm32f1xx_hal_timebase_tim.o
      1388         10          0          0          0       7260   stm32f1xx_hal_uart.o
       522        290          0          0          0       4358   stm32f1xx_it.o
         0          0          0          0          0       8924   stream_buffer.o
         2          0         24          4          0       1083   system_stm32f1xx.o
      2796        346          0         60       1220      23398   tasks.o
      1238        124          0         20        280      26471   timers.o
       404         46          0          0        136       2315   usart.o

    ----------------------------------------------------------------------
     16346       <USER>        <GROUP>        148       8624     712879   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          6          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1142         54          0          0          0        352   printf8.o
         0          0          0          4          0          0   stdout.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1470         <USER>          <GROUP>          4          0        824   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1464         70          0          4          0        824   mc_w.l

    ----------------------------------------------------------------------
      1470         <USER>          <GROUP>          4          0        824   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17816       1604        408        152       8624     705639   Grand Totals
     17816       1604        408        152       8624     705639   ELF Image Totals
     17816       1604        408        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                18224 (  17.80kB)
    Total RW  Size (RW Data + ZI Data)              8776 (   8.57kB)
    Total ROM Size (Code + RO Data + RW Data)      18376 (  17.95kB)

==============================================================================

