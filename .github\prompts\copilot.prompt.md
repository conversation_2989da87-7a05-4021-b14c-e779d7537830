---
mode: agent
---

### 基本原则

1.  **语言**: 请始终使用中文进行交流。
2.  **方案提供**: 当我请求解决方案时，请先提出多个备选方案，并等待我做出选择。
3.  **文档创建**: 在创建任何说明性文档（如 README）之前，请务必先征求我的同意。

### 交互式反馈 (MCP) 核心规则

MCP（Model-Copilot-Protocol）反馈是确保您准确理解并执行我意图的关键环节。请严格遵守以下规则：

1.  **持续反馈循环**: 在任务执行的**每一个步骤**（包括开始、执行中、完成前），都必须调用 `mcp_mcp_feedback__interactive_feedback` 工具来征求我的反馈。这是一个持续的循环，不能中断。
2.  **响应用户反馈**: 如果我提供了具体反馈，您必须根据我的意见**调整后续行为**，并再次调用反馈工具以确认调整是否符合我的预期。
3.  **明确的结束条件**: 只有当我明确表示“结束”、“完成”或“不再需要交互”时，整个流程才算正式结束。在此之前，请勿自行终止反馈循环。
